@echo off
echo 编译OneNET Qt上位机程序...

REM 设置Qt环境变量（根据实际Qt安装路径调整）
set QT_DIR=C:\Qt\Qt5.14.2\5.14.2\msvc2017
set PATH=%QT_DIR%\bin;%PATH%

REM 检查Qt是否可用
where qmake >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 找不到qmake，请检查Qt安装路径
    echo 请修改build.bat中的QT_DIR变量
    pause
    exit /b 1
)

echo Qt路径: %QT_DIR%

REM 清理之前的构建文件
if exist Makefile del Makefile
if exist OneNET_Client.exe del OneNET_Client.exe
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release

REM 生成Makefile
echo 正在生成Makefile...
qmake OneNET_Client.pro
if %errorlevel% neq 0 (
    echo 错误: qmake失败
    pause
    exit /b 1
)

REM 编译程序
echo 正在编译程序...
nmake release
if %errorlevel% neq 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

REM 复制必要的DLL文件
echo 正在复制依赖文件...
if exist release\OneNET_Client.exe (
    copy release\OneNET_Client.exe .
    copy Qt5Mqtt.dll .
    echo 编译成功！生成文件: OneNET_Client.exe
) else (
    echo 错误: 未找到编译后的可执行文件
    pause
    exit /b 1
)

echo.
echo 编译完成！
echo 使用方法：
echo 1. 直接运行: OneNET_Client.exe
echo 2. 使用配置启动: start_onenet.bat
echo.
pause