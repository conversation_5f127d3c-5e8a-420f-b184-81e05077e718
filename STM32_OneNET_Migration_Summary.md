# STM32代码OneNET迁移总结

## 修改概述
成功将STM32F103的MQTT连接从broker.mqttdashboard.com迁移到OneNET平台(183.230.40.39:6002)。

## 具体修改内容

### 1. 添加OneNET配置常量 (第35-38行)
```c
// OneNET平台配置常量
const char *PRODUCT_ID = "cF16DWy2B8";     //OneNET产品ID
const char *DEVICE_NAME = "stm32f103";     //OneNET设备名称
static uint32_t msg_id = 1;                //消息ID计数器
```

### 2. 修改MQTT用户配置 (第126行)
**原代码:**
```c
AT+MQTTUSERCFG=0,1,"ESP","89Mif","version=2018-10-31&res=proD",0,0,""
```

**修改为:**
```c
AT+MQTTUSERCFG=0,1,"stm32f103","cF16DWy2B8","version=2018-10-31&res=products%2FcF16DWy2B8%2Fdevices%2Fstm32f103&et=2066666954&method=md5&sign=%2FpsAUgjyYBfZO53tn%2FYK7Q%3D%3D",0,0,""
```

### 3. 修改MQTT服务器连接 (第128行)
**原代码:**
```c
AT+MQTTCONN=0,"broker.mqttdashboard.com",1883,1
```

**修改为:**
```c
AT+MQTTCONN=0,"183.230.40.39",6002,1
```

### 4. 修改测试消息主题 (第130行)
**原代码:**
```c
sprintf(T_data,"AT+MQTTPUB=0,\"nitamade/jiushe/ge/shabi\",\"%s\",1,0\r\n","TT=657");
```

**修改为:**
```c
sprintf(T_data,"AT+MQTTPUB=0,\"test/topic\",\"TEST_DATA\",1,0\r\n");
```

### 5. 重写主循环数据处理逻辑 (第143-169行)
**新增功能:**
- 解析电表数据格式: `A{voltage}?B{current}?C{frequency}?D{active_power}?E{total_energy}`
- 构造OneNET物模型JSON格式
- 使用OneNET标准主题: `$sys/{PRODUCT_ID}/{DEVICE_NAME}/thing/property/post`
- 自动递增消息ID

**JSON数据格式:**
```json
{
  "id": "消息ID",
  "version": "1.0",
  "params": {
    "voltage": 220.50,
    "current": 1.20,
    "frequency": 50.00,
    "active_power": 264.6,
    "total_energy": 123.45
  }
}
```

### 6. 优化系统性能 (第170行)
将主循环延时从1ms增加到10ms，减少CPU负载。

## OneNET认证参数
- **产品ID**: cF16DWy2B8
- **设备名称**: stm32f103
- **MQTT服务器**: 183.230.40.39:6002
- **认证Token**: version=2018-10-31&res=products%2FcF16DWy2B8%2Fdevices%2Fstm32f103&et=2066666954&method=md5&sign=%2FpsAUgjyYBfZO53tn%2FYK7Q%3D%3D

## 数据流程
1. STM32通过UART1接收电表数据
2. 解析数据格式并提取电压、电流、频率、功率、电能参数
3. 构造OneNET物模型JSON格式
4. 通过ESP8266发送到OneNET MQTT服务器
5. Qt上位机从OneNET平台接收并显示数据

## 兼容性说明
- 保持原有的UART通信协议不变
- 保持原有的数据解析格式不变
- 仅修改MQTT连接和数据发布部分
- 向后兼容原有的电表数据格式

## 测试建议
1. 验证ESP8266能够连接到OneNET服务器
2. 确认数据能够正确发布到OneNET主题
3. 测试Qt上位机能够接收并解析数据
4. 验证系统的稳定性和重连机制