#include "mainwindow.h"
#include <QApplication>
#include <QMenuBar>
#include <QStatusBar>
#include <QDateTime>
#include <QMessageBox>
#include <QGridLayout>
#include <QFormLayout>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mqttClient(new MqttClient(this))
    , m_updateTimer(new QTimer(this))
    , m_messageCount(0)
{
    setupUI();
    setupMenuBar();
    
    // 连接MQTT客户端信号
    connect(m_mqttClient, &MqttClient::connected, this, &MainWindow::onMqttConnected);
    connect(m_mqttClient, &MqttClient::disconnected, this, &MainWindow::onMqttDisconnected);
    connect(m_mqttClient, &MqttClient::messageReceived, this, &MainWindow::onMessageReceived);
    connect(m_mqttClient, &MqttClient::connectionStatusChanged, this, &MainWindow::onConnectionStatusChanged);
    
    // 设置更新定时器
    connect(m_updateTimer, &QTimer::timeout, [this]() {
        m_lastUpdateLabel->setText("最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    });
    m_updateTimer->start(1000);
    
    // 初始化UI状态
    updateConnectionUI();
    logMessage("OneNET智能电表上位机已启动");
    
    // 自动连接
    QTimer::singleShot(1000, this, &MainWindow::connectMqtt);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // 设置窗口属性
    setWindowTitle("OneNET智能电表上位机 v1.0");
    setMinimumSize(800, 600);
    
    // 创建连接控制区域
    m_connectionGroup = new QGroupBox("MQTT连接配置");
    QFormLayout *connLayout = new QFormLayout(m_connectionGroup);
    
    m_hostEdit = new QLineEdit("*************");
    m_portSpin = new QSpinBox();
    m_portSpin->setRange(1, 65535);
    m_portSpin->setValue(6002);
    m_clientIdEdit = new QLineEdit("stm32f103");
    m_usernameEdit = new QLineEdit("cF16DWy2B8");
    m_passwordEdit = new QLineEdit();
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    
    connLayout->addRow("服务器地址:", m_hostEdit);
    connLayout->addRow("端口:", m_portSpin);
    connLayout->addRow("客户端ID:", m_clientIdEdit);
    connLayout->addRow("用户名:", m_usernameEdit);
    connLayout->addRow("密码:", m_passwordEdit);
    
    // 连接按钮
    QHBoxLayout *btnLayout = new QHBoxLayout();
    m_connectBtn = new QPushButton("连接");
    m_disconnectBtn = new QPushButton("断开");
    m_statusLabel = new QLabel("未连接");
    
    connect(m_connectBtn, &QPushButton::clicked, this, &MainWindow::connectMqtt);
    connect(m_disconnectBtn, &QPushButton::clicked, this, &MainWindow::disconnectMqtt);
    
    btnLayout->addWidget(m_connectBtn);
    btnLayout->addWidget(m_disconnectBtn);
    btnLayout->addWidget(new QLabel("状态:"));
    btnLayout->addWidget(m_statusLabel);
    btnLayout->addStretch();
    
    connLayout->addRow(btnLayout);
    m_mainLayout->addWidget(m_connectionGroup);
    
    // 创建数据显示区域
    m_dataGroup = new QGroupBox("电表数据");
    QGridLayout *dataLayout = new QGridLayout(m_dataGroup);
    
    m_voltageLabel = new QLabel("电压: -- V");
    m_currentLabel = new QLabel("电流: -- A");
    m_powerLabel = new QLabel("功率: -- W");
    m_energyLabel = new QLabel("电能: -- kWh");
    m_lastUpdateLabel = new QLabel("最后更新: --");
    
    // 设置标签样式
    QString labelStyle = "QLabel { font-size: 14px; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #f0f0f0; }";
    m_voltageLabel->setStyleSheet(labelStyle);
    m_currentLabel->setStyleSheet(labelStyle);
    m_powerLabel->setStyleSheet(labelStyle);
    m_energyLabel->setStyleSheet(labelStyle);
    
    dataLayout->addWidget(m_voltageLabel, 0, 0);
    dataLayout->addWidget(m_currentLabel, 0, 1);
    dataLayout->addWidget(m_powerLabel, 1, 0);
    dataLayout->addWidget(m_energyLabel, 1, 1);
    dataLayout->addWidget(m_lastUpdateLabel, 2, 0, 1, 2);
    
    m_mainLayout->addWidget(m_dataGroup);
    
    // 创建日志区域
    m_logGroup = new QGroupBox("运行日志");
    QVBoxLayout *logLayout = new QVBoxLayout(m_logGroup);
    
    m_logEdit = new QTextEdit();
    m_logEdit->setMaximumHeight(200);
    m_logEdit->setReadOnly(true);
    
    QHBoxLayout *logBtnLayout = new QHBoxLayout();
    m_clearLogBtn = new QPushButton("清空日志");
    m_testBtn = new QPushButton("发送测试消息");
    
    connect(m_clearLogBtn, &QPushButton::clicked, this, &MainWindow::clearLog);
    connect(m_testBtn, &QPushButton::clicked, this, &MainWindow::sendTestMessage);
    
    logBtnLayout->addWidget(m_clearLogBtn);
    logBtnLayout->addWidget(m_testBtn);
    logBtnLayout->addStretch();
    
    logLayout->addWidget(m_logEdit);
    logLayout->addLayout(logBtnLayout);
    
    m_mainLayout->addWidget(m_logGroup);
}

void MainWindow::setupMenuBar()
{
    QMenuBar *menuBar = this->menuBar();
    
    // 文件菜单
    QMenu *fileMenu = menuBar->addMenu("文件(&F)");
    QAction *exitAction = fileMenu->addAction("退出(&X)");
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    
    // 帮助菜单
    QMenu *helpMenu = menuBar->addMenu("帮助(&H)");
    QAction *aboutAction = helpMenu->addAction("关于(&A)");
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "关于", 
            "OneNET智能电表上位机 v1.0\n\n"
            "基于Qt5和MQTT协议\n"
            "连接OneNET物联网平台\n"
            "接收STM32智能电表数据");
    });
    
    // 状态栏
    statusBar()->showMessage("就绪");
}

void MainWindow::connectMqtt()
{
    logMessage("正在连接OneNET MQTT服务器...");
    m_mqttClient->connectToServer();
}

void MainWindow::disconnectMqtt()
{
    logMessage("断开MQTT连接");
    m_mqttClient->disconnectFromServer();
}

void MainWindow::onMqttConnected()
{
    logMessage("MQTT连接成功！");
    updateConnectionUI();
    statusBar()->showMessage("MQTT已连接");
}

void MainWindow::onMqttDisconnected()
{
    logMessage("MQTT连接已断开");
    updateConnectionUI();
    statusBar()->showMessage("MQTT已断开");
}

void MainWindow::onMessageReceived(const QString &topic, const QString &message)
{
    m_messageCount++;
    logMessage(QString("收到消息 [%1]: %2").arg(topic, message));
    
    // 解析电表数据
    parseElectricMeterData(message);
    
    statusBar()->showMessage(QString("已接收 %1 条消息").arg(m_messageCount));
}

void MainWindow::onConnectionStatusChanged(const QString &status)
{
    m_statusLabel->setText(status);
    
    if (status.contains("错误")) {
        m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    } else if (status == "已连接") {
        m_statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
    } else {
        m_statusLabel->setStyleSheet("QLabel { color: orange; font-weight: bold; }");
    }
}

void MainWindow::sendTestMessage()
{
    if (m_mqttClient->isConnected()) {
        QString testData = QString("{\"id\":\"test_%1\",\"voltage\":220.5,\"current\":1.2,\"power\":264.6,\"energy\":123.45}")
                          .arg(QDateTime::currentMSecsSinceEpoch());
        
        m_mqttClient->publish("$sys/cF16DWy2B8/stm32f103/thing/property/post", testData);
        logMessage("发送测试数据: " + testData);
    } else {
        logMessage("错误: MQTT未连接，无法发送测试消息");
    }
}

void MainWindow::clearLog()
{
    m_logEdit->clear();
    logMessage("日志已清空");
}

void MainWindow::updateConnectionUI()
{
    bool connected = m_mqttClient->isConnected();
    m_connectBtn->setEnabled(!connected);
    m_disconnectBtn->setEnabled(connected);
    m_testBtn->setEnabled(connected);
}

void MainWindow::logMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);
    m_logEdit->append(logEntry);
    
    // 自动滚动到底部
    QTextCursor cursor = m_logEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logEdit->setTextCursor(cursor);
}

void MainWindow::parseElectricMeterData(const QString &data)
{
    // 尝试解析JSON格式的数据
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data.toUtf8(), &error);
    
    if (error.error == QJsonParseError::NoError && doc.isObject()) {
        QJsonObject obj = doc.object();
        
        // 解析电表参数
        if (obj.contains("voltage")) {
            double voltage = obj["voltage"].toDouble();
            m_voltageLabel->setText(QString("电压: %1 V").arg(voltage, 0, 'f', 1));
        }
        
        if (obj.contains("current")) {
            double current = obj["current"].toDouble();
            m_currentLabel->setText(QString("电流: %1 A").arg(current, 0, 'f', 2));
        }
        
        if (obj.contains("power")) {
            double power = obj["power"].toDouble();
            m_powerLabel->setText(QString("功率: %1 W").arg(power, 0, 'f', 1));
        }
        
        if (obj.contains("energy")) {
            double energy = obj["energy"].toDouble();
            m_energyLabel->setText(QString("电能: %1 kWh").arg(energy, 0, 'f', 2));
        }
        
        m_lastUpdateLabel->setText("最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    } else {
        // 如果不是JSON格式，尝试解析简单的文本格式
        // 假设数据格式为: "220.5,1.2,264.6,123.45" (电压,电流,功率,电能)
        QStringList values = data.split(',');
        if (values.size() >= 4) {
            bool ok;
            double voltage = values[0].toDouble(&ok);
            if (ok) m_voltageLabel->setText(QString("电压: %1 V").arg(voltage, 0, 'f', 1));
            
            double current = values[1].toDouble(&ok);
            if (ok) m_currentLabel->setText(QString("电流: %1 A").arg(current, 0, 'f', 2));
            
            double power = values[2].toDouble(&ok);
            if (ok) m_powerLabel->setText(QString("功率: %1 W").arg(power, 0, 'f', 1));
            
            double energy = values[3].toDouble(&ok);
            if (ok) m_energyLabel->setText(QString("电能: %1 kWh").arg(energy, 0, 'f', 2));
            
            m_lastUpdateLabel->setText("最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
        }
    }
}