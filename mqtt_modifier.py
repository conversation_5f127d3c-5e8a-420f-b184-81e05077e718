#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT配置修改工具 - 将学长的程序适配OneNET
"""

import os
import shutil

def modify_mqtt_config():
    """修改MQTT配置"""
    source_path = r"C:\Users\<USER>\Desktop\package\ShengFan.exe"
    output_path = r"C:\Users\<USER>\Desktop\package\ShengFan_onenet.exe"
    
    print("🚀 MQTT配置修改工具")
    print("=" * 50)
    
    # 检查源文件
    if not os.path.exists(source_path):
        print(f"❌ 源文件不存在: {source_path}")
        return False
    
    try:
        # 读取文件
        with open(source_path, 'rb') as f:
            data = f.read()
        
        print(f"📁 文件大小: {len(data)} 字节")
        
        # 查找目标字符串
        broker_bytes = b'broker.mqttdashboard.com'
        topic_bytes = b'nitamade/jiushe/ge/shabi'
        
        broker_found = broker_bytes in data
        topic_found = topic_bytes in data
        
        print(f"🔍 搜索结果:")
        print(f"   MQTT服务器: {'✅ 找到' if broker_found else '❌ 未找到'}")
        print(f"   MQTT主题: {'✅ 找到' if topic_found else '❌ 未找到'}")
        
        if not (broker_found or topic_found):
            print("❌ 没有找到需要修改的配置")
            return False
        
        # 执行替换
        modified_data = data
        replacement_count = 0
        
        if broker_found:
            # 替换服务器地址
            new_broker = b'*************'
            # 保持相同长度，用空字节填充
            padding_length = len(broker_bytes) - len(new_broker)
            new_broker_padded = new_broker + b'\x00' * padding_length
            
            modified_data = modified_data.replace(broker_bytes, new_broker_padded)
            replacement_count += 1
            print(f"✅ 替换服务器: broker.mqttdashboard.com -> *************")
        
        if topic_found:
            # 替换主题
            new_topic = b'$dp'
            # 保持相同长度，用空字节填充
            padding_length = len(topic_bytes) - len(new_topic)
            new_topic_padded = new_topic + b'\x00' * padding_length
            
            modified_data = modified_data.replace(topic_bytes, new_topic_padded)
            replacement_count += 1
            print(f"✅ 替换主题: nitamade/jiushe/ge/shabi -> $dp")
        
        # 保存修改后的文件
        with open(output_path, 'wb') as f:
            f.write(modified_data)
        
        print(f"✅ 修改完成: {output_path}")
        print(f"📊 总共替换了 {replacement_count} 处配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return False

def create_config_guide():
    """创建配置指南"""
    guide_content = """# OneNET配置修改完成指南

## 修改内容总结：
✅ MQTT服务器地址: broker.mqttdashboard.com -> *************
✅ MQTT主题: nitamade/jiushe/ge/shabi -> $dp

## 您的OneNET配置信息：
- 产品ID: cF16DWy2B8
- 设备ID: stm32f103
- 访问密钥: QU9uYW44bG5pcG1NVkhLWmNPUnNGZXVVcHhDZEtQMkM=
- MQTT服务器: *************
- MQTT端口: 6002 (需要在STM32代码中修改)

## 下一步操作：

### 1. 测试修改后的程序
- 运行 ShengFan_onenet.exe
- 检查程序是否正常启动
- 查看是否有错误提示

### 2. 确保STM32代码配置正确
确保您的STM32代码中包含以下配置：
```c
// OneNET MQTT服务器配置
AT+MQTTCONN=0,"*************",6002,1

// OneNET认证配置
AT+MQTTUSERCFG=0,1,"cF16DWy2B8","stm32f103","YOUR_ACCESS_KEY",0,0,""

// 数据发布格式 (OneNET数据点格式)
AT+MQTTPUB=0,"$dp","{"id":123,"dp":{"temp":[{"v":25.6}]}}",1,0
```

### 3. 数据格式说明
OneNET要求的数据格式为JSON：
```json
{
  "id": 123,
  "dp": {
    "temp": [{"v": 25.6}],
    "humidity": [{"v": 60.5}]
  }
}
```

### 4. 如果遇到问题：
1. 使用备份文件 ShengFan_backup.exe 恢复原程序
2. 检查OneNET平台设备状态
3. 验证网络连接
4. 确认STM32代码中的端口为6002

### 5. 验证连接成功：
- 在OneNET平台查看设备在线状态
- 检查数据是否正常上传
- 上位机是否能接收到数据

## 文件说明：
- ShengFan.exe: 原始程序
- ShengFan_backup.exe: 备份文件
- ShengFan_onenet.exe: 修改后的程序 (使用这个)

## 技术支持：
如果遇到问题，请检查：
1. 网络连接是否正常
2. OneNET平台配置是否正确
3. STM32代码是否已更新端口号
4. 数据格式是否符合OneNET要求
"""
    
    with open(r"C:\Users\<USER>\Desktop\package\OneNET配置指南.txt", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📝 配置指南已创建: OneNET配置指南.txt")

def main():
    """主函数"""
    success = modify_mqtt_config()
    
    if success:
        create_config_guide()
        print("\n🎉 修改完成！")
        print("\n📋 重要提醒:")
        print("1. 请测试 ShengFan_onenet.exe 是否能正常启动")
        print("2. 确保STM32代码中端口改为6002")
        print("3. 查看 OneNET配置指南.txt 了解详细步骤")
        print("4. 如有问题，使用 ShengFan_backup.exe 恢复")
    else:
        print("\n❌ 修改失败，请检查文件或联系技术支持")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
