#include <QApplication>
#include <QSettings>
#include <QDir>
#include <QDebug>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("OneNET_Client");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("SmartMeter");
    
    // 检查配置文件
    QString configPath = QDir::currentPath() + "/mqtt_config.ini";
    qDebug() << "配置文件路径:" << configPath;
    
    if (!QFile::exists(configPath)) {
        qDebug() << "警告: 配置文件不存在，使用默认配置";
    }
    
    // 创建主窗口
    MainWindow window;
    window.show();
    
    qDebug() << "OneNET客户端已启动";
    
    return app.exec();
}