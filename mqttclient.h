#ifndef MQTTCLIENT_H
#define MQTTCLIENT_H

#include <QObject>
#include <QMqttClient>
#include <QSettings>
#include <QTimer>
#include <QDebug>

class MqttClient : public QObject
{
    Q_OBJECT

public:
    explicit MqttClient(QObject *parent = nullptr);
    ~MqttClient();
    
    // 连接控制
    void connectToServer();
    void disconnectFromServer();
    bool isConnected() const;
    
    // 消息发布和订阅
    void subscribe(const QString &topic);
    void publish(const QString &topic, const QString &message);
    
    // 配置管理
    void loadConfig();
    void saveConfig();

signals:
    void connected();
    void disconnected();
    void messageReceived(const QString &topic, const QString &message);
    void connectionStatusChanged(const QString &status);

private slots:
    void onConnected();
    void onDisconnected();
    void onMessageReceived(const QByteArray &message, const QMqttTopicName &topic);
    void onStateChanged(QMqttClient::ClientState state);
    void onErrorChanged(QMqttClient::ClientError error);
    void reconnectTimer();

private:
    QMqttClient *m_client;          // MQTT客户端
    QSettings *m_settings;          // 配置管理
    QTimer *m_reconnectTimer;       // 重连定时器
    
    // 连接参数
    QString m_host;
    quint16 m_port;
    QString m_clientId;
    QString m_username;
    QString m_password;
    QString m_subscribeTopic;
    QString m_publishTopic;
    
    // 状态管理
    bool m_autoReconnect;
    int m_reconnectInterval;
    
    void setupClient();
    void loadDefaultConfig();
};

#endif // MQTTCLIENT_H