#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTextEdit>
#include <QLabel>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGroupBox>
#include <QLineEdit>
#include <QSpinBox>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include "mqttclient.h"

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void connectMqtt();
    void disconnectMqtt();
    void onMqttConnected();
    void onMqttDisconnected();
    void onMessageReceived(const QString &topic, const QString &message);
    void onConnectionStatusChanged(const QString &status);
    void sendTestMessage();
    void clearLog();

private:
    void setupUI();
    void setupMenuBar();
    void updateConnectionUI();
    void logMessage(const QString &message);
    void parseElectricMeterData(const QString &data);

    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    
    // 连接控制区域
    QGroupBox *m_connectionGroup;
    QLineEdit *m_hostEdit;
    QSpinBox *m_portSpin;
    QLineEdit *m_clientIdEdit;
    QLineEdit *m_usernameEdit;
    QLineEdit *m_passwordEdit;
    QPushButton *m_connectBtn;
    QPushButton *m_disconnectBtn;
    QLabel *m_statusLabel;
    
    // 数据显示区域
    QGroupBox *m_dataGroup;
    QLabel *m_voltageLabel;
    QLabel *m_currentLabel;
    QLabel *m_powerLabel;
    QLabel *m_energyLabel;
    QLabel *m_lastUpdateLabel;
    
    // 日志区域
    QGroupBox *m_logGroup;
    QTextEdit *m_logEdit;
    QPushButton *m_clearLogBtn;
    QPushButton *m_testBtn;
    
    // MQTT客户端
    MqttClient *m_mqttClient;
    
    // 状态管理
    QTimer *m_updateTimer;
    int m_messageCount;
};

#endif // MAINWINDOW_H