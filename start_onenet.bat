@echo off
echo 启动OneNET版本的Qt上位机程序...

REM 设置OneNET MQTT连接环境变量
set MQTT_HOST=*************
set MQTT_PORT=6002
set MQTT_CLIENT_ID=stm32f103
set MQTT_USERNAME=cF16DWy2B8
set MQTT_PASSWORD=version=2018-10-31^&res=products%%2FcF16DWy2B8%%2Fdevices%%2Fstm32f103^&et=2066666954^&method=md5^&sign=%%2FpsAUgjyYBfZO53tn%%2FYK7Q%%3D%%3D

REM 设置OneNET主题
set MQTT_SUB_TOPIC=$sys/cF16DWy2B8/stm32f103/thing/property/set
set MQTT_PUB_TOPIC=$sys/cF16DWy2B8/stm32f103/thing/property/post

REM 设置配置文件路径
set MQTT_CONFIG_FILE=%~dp0mqtt_config.ini

echo MQTT服务器: %MQTT_HOST%:%MQTT_PORT%
echo 客户端ID: %MQTT_CLIENT_ID%
echo 用户名: %MQTT_USERNAME%
echo 配置文件: %MQTT_CONFIG_FILE%

REM 启动Qt应用程序
echo 正在启动ShengFan.exe...
start "" "%~dp0ShengFan.exe"

echo Qt上位机程序已启动，OneNET配置已加载
pause