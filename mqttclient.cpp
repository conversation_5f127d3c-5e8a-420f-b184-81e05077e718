#include "mqttclient.h"
#include <QCoreApplication>
#include <QDir>

MqttClient::MqttClient(QObject *parent)
    : QObject(parent)
    , m_client(nullptr)
    , m_settings(nullptr)
    , m_reconnectTimer(new QTimer(this))
    , m_port(6002)
    , m_autoReconnect(true)
    , m_reconnectInterval(5000)
{
    setupClient();
    loadConfig();
    
    // 设置重连定时器
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &MqttClient::reconnectTimer);
}

MqttClient::~MqttClient()
{
    if (m_client && m_client->state() == QMqttClient::Connected) {
        m_client->disconnectFromHost();
    }
    delete m_settings;
}

void MqttClient::setupClient()
{
    m_client = new QMqttClient(this);
    
    // 连接信号
    connect(m_client, &QMqttClient::connected, this, &MqttClient::onConnected);
    connect(m_client, &QMqttClient::disconnected, this, &MqttClient::onDisconnected);
    connect(m_client, &QMqttClient::messageReceived, this, &MqttClient::onMessageReceived);
    connect(m_client, &QMqttClient::stateChanged, this, &MqttClient::onStateChanged);
    connect(m_client, &QMqttClient::errorChanged, this, &MqttClient::onErrorChanged);
}

void MqttClient::loadConfig()
{
    // 优先从环境变量读取配置
    QString host = qgetenv("MQTT_HOST");
    QString port = qgetenv("MQTT_PORT");
    QString clientId = qgetenv("MQTT_CLIENT_ID");
    QString username = qgetenv("MQTT_USERNAME");
    QString password = qgetenv("MQTT_PASSWORD");
    
    if (!host.isEmpty()) {
        m_host = host;
        m_port = port.toUInt();
        m_clientId = clientId;
        m_username = username;
        m_password = password;
        qDebug() << "从环境变量加载MQTT配置";
        return;
    }
    
    // 从配置文件读取
    QString configPath = QDir::currentPath() + "/mqtt_config.ini";
    m_settings = new QSettings(configPath, QSettings::IniFormat);
    
    m_host = m_settings->value("MQTT/host", "*************").toString();
    m_port = m_settings->value("MQTT/port", 6002).toUInt();
    m_clientId = m_settings->value("MQTT/clientId", "stm32f103").toString();
    m_username = m_settings->value("MQTT/username", "cF16DWy2B8").toString();
    m_password = m_settings->value("MQTT/password", "").toString();
    m_subscribeTopic = m_settings->value("MQTT/subscribe_topic", "$sys/cF16DWy2B8/stm32f103/thing/property/set").toString();
    m_publishTopic = m_settings->value("MQTT/publish_topic", "$sys/cF16DWy2B8/stm32f103/thing/property/post").toString();
    
    qDebug() << "从配置文件加载MQTT配置:" << configPath;
}

void MqttClient::connectToServer()
{
    if (m_client->state() == QMqttClient::Connected) {
        qDebug() << "MQTT已连接";
        return;
    }
    
    // 设置连接参数
    m_client->setHostname(m_host);
    m_client->setPort(m_port);
    m_client->setClientId(m_clientId);
    m_client->setUsername(m_username);
    m_client->setPassword(m_password);
    
    qDebug() << "正在连接OneNET MQTT服务器:" << m_host << ":" << m_port;
    qDebug() << "客户端ID:" << m_clientId;
    qDebug() << "用户名:" << m_username;
    
    m_client->connectToHost();
}

void MqttClient::disconnectFromServer()
{
    if (m_client->state() == QMqttClient::Connected) {
        m_client->disconnectFromHost();
    }
}

bool MqttClient::isConnected() const
{
    return m_client && m_client->state() == QMqttClient::Connected;
}

void MqttClient::subscribe(const QString &topic)
{
    if (isConnected()) {
        auto subscription = m_client->subscribe(topic);
        if (!subscription) {
            qDebug() << "订阅失败:" << topic;
        } else {
            qDebug() << "订阅成功:" << topic;
        }
    }
}

void MqttClient::publish(const QString &topic, const QString &message)
{
    if (isConnected()) {
        m_client->publish(topic, message.toUtf8());
        qDebug() << "发布消息到" << topic << ":" << message;
    }
}

void MqttClient::onConnected()
{
    qDebug() << "MQTT连接成功";
    emit connected();
    emit connectionStatusChanged("已连接");
    
    // 自动订阅主题
    if (!m_subscribeTopic.isEmpty()) {
        subscribe(m_subscribeTopic);
    }
}

void MqttClient::onDisconnected()
{
    qDebug() << "MQTT连接断开";
    emit disconnected();
    emit connectionStatusChanged("已断开");
    
    // 自动重连
    if (m_autoReconnect) {
        qDebug() << "将在" << m_reconnectInterval/1000 << "秒后重连";
        m_reconnectTimer->start(m_reconnectInterval);
    }
}

void MqttClient::onMessageReceived(const QByteArray &message, const QMqttTopicName &topic)
{
    QString topicStr = topic.name();
    QString messageStr = QString::fromUtf8(message);
    
    qDebug() << "收到消息 [" << topicStr << "]:" << messageStr;
    emit messageReceived(topicStr, messageStr);
}

void MqttClient::onStateChanged(QMqttClient::ClientState state)
{
    QString stateStr;
    switch (state) {
    case QMqttClient::Disconnected:
        stateStr = "断开连接";
        break;
    case QMqttClient::Connecting:
        stateStr = "正在连接";
        break;
    case QMqttClient::Connected:
        stateStr = "已连接";
        break;
    }
    
    qDebug() << "MQTT状态变化:" << stateStr;
    emit connectionStatusChanged(stateStr);
}

void MqttClient::onErrorChanged(QMqttClient::ClientError error)
{
    QString errorStr;
    switch (error) {
    case QMqttClient::NoError:
        return;
    case QMqttClient::InvalidProtocolVersion:
        errorStr = "协议版本无效";
        break;
    case QMqttClient::IdRejected:
        errorStr = "客户端ID被拒绝";
        break;
    case QMqttClient::ServerUnavailable:
        errorStr = "服务器不可用";
        break;
    case QMqttClient::BadUsernameOrPassword:
        errorStr = "用户名或密码错误";
        break;
    case QMqttClient::NotAuthorized:
        errorStr = "未授权";
        break;
    default:
        errorStr = "未知错误";
        break;
    }
    
    qDebug() << "MQTT错误:" << errorStr;
    emit connectionStatusChanged("错误: " + errorStr);
}

void MqttClient::reconnectTimer()
{
    qDebug() << "尝试重新连接...";
    connectToServer();
}