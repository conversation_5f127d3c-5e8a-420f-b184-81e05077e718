# PowerShell脚本：修改MQTT配置
param()

Write-Host "🚀 MQTT配置修改工具" -ForegroundColor Green
Write-Host "=" * 50

$sourcePath = "C:\Users\<USER>\Desktop\package\ShengFan.exe"
$outputPath = "C:\Users\<USER>\Desktop\package\ShengFan_onenet.exe"

# 检查源文件
if (-not (Test-Path $sourcePath)) {
    Write-Host "❌ 源文件不存在: $sourcePath" -ForegroundColor Red
    exit 1
}

Write-Host "📁 找到源文件: $sourcePath" -ForegroundColor Green

try {
    # 读取文件
    $bytes = [System.IO.File]::ReadAllBytes($sourcePath)
    Write-Host "📁 文件大小: $($bytes.Length) 字节" -ForegroundColor Cyan
    
    # 定义要替换的字节序列
    $brokerOld = [System.Text.Encoding]::UTF8.GetBytes("broker.mqttdashboard.com")
    $topicOld = [System.Text.Encoding]::UTF8.GetBytes("nitamade/jiushe/ge/shabi")
    
    # 查找目标字符串
    $brokerFound = $false
    $topicFound = $false
    
    for ($i = 0; $i -le ($bytes.Length - $brokerOld.Length); $i++) {
        $match = $true
        for ($j = 0; $j -lt $brokerOld.Length; $j++) {
            if ($bytes[$i + $j] -ne $brokerOld[$j]) {
                $match = $false
                break
            }
        }
        if ($match) {
            $brokerFound = $true
            break
        }
    }
    
    for ($i = 0; $i -le ($bytes.Length - $topicOld.Length); $i++) {
        $match = $true
        for ($j = 0; $j -lt $topicOld.Length; $j++) {
            if ($bytes[$i + $j] -ne $topicOld[$j]) {
                $match = $false
                break
            }
        }
        if ($match) {
            $topicFound = $true
            break
        }
    }
    
    Write-Host "🔍 搜索结果:" -ForegroundColor Yellow
    Write-Host "   MQTT服务器: $(if($brokerFound){'✅ 找到'}else{'❌ 未找到'})" -ForegroundColor $(if($brokerFound){"Green"}else{"Red"})
    Write-Host "   MQTT主题: $(if($topicFound){'✅ 找到'}else{'❌ 未找到'})" -ForegroundColor $(if($topicFound){"Green"}else{"Red"})
    
    if (-not ($brokerFound -or $topicFound)) {
        Write-Host "❌ 没有找到需要修改的配置" -ForegroundColor Red
        exit 1
    }
    
    # 创建修改后的字节数组
    $modifiedBytes = $bytes.Clone()
    $replacementCount = 0
    
    if ($brokerFound) {
        # 替换服务器地址
        $brokerNew = [System.Text.Encoding]::UTF8.GetBytes("*************")
        $padding = New-Object byte[] ($brokerOld.Length - $brokerNew.Length)
        $brokerNewPadded = $brokerNew + $padding
        
        # 查找并替换
        for ($i = 0; $i -le ($modifiedBytes.Length - $brokerOld.Length); $i++) {
            $match = $true
            for ($j = 0; $j -lt $brokerOld.Length; $j++) {
                if ($modifiedBytes[$i + $j] -ne $brokerOld[$j]) {
                    $match = $false
                    break
                }
            }
            if ($match) {
                for ($j = 0; $j -lt $brokerNewPadded.Length; $j++) {
                    $modifiedBytes[$i + $j] = $brokerNewPadded[$j]
                }
                $replacementCount++
                Write-Host "✅ 替换服务器: broker.mqttdashboard.com -> *************" -ForegroundColor Green
                break
            }
        }
    }
    
    if ($topicFound) {
        # 替换主题
        $topicNew = [System.Text.Encoding]::UTF8.GetBytes("`$dp")
        $padding = New-Object byte[] ($topicOld.Length - $topicNew.Length)
        $topicNewPadded = $topicNew + $padding
        
        # 查找并替换
        for ($i = 0; $i -le ($modifiedBytes.Length - $topicOld.Length); $i++) {
            $match = $true
            for ($j = 0; $j -lt $topicOld.Length; $j++) {
                if ($modifiedBytes[$i + $j] -ne $topicOld[$j]) {
                    $match = $false
                    break
                }
            }
            if ($match) {
                for ($j = 0; $j -lt $topicNewPadded.Length; $j++) {
                    $modifiedBytes[$i + $j] = $topicNewPadded[$j]
                }
                $replacementCount++
                Write-Host "✅ 替换主题: nitamade/jiushe/ge/shabi -> `$dp" -ForegroundColor Green
                break
            }
        }
    }
    
    # 保存修改后的文件
    [System.IO.File]::WriteAllBytes($outputPath, $modifiedBytes)
    Write-Host "✅ 修改完成: $outputPath" -ForegroundColor Green
    Write-Host "📊 总共替换了 $replacementCount 处配置" -ForegroundColor Cyan
    
    # 创建配置指南
    $guideContent = @"
# OneNET配置修改完成指南

## 修改内容总结：
✅ MQTT服务器地址: broker.mqttdashboard.com -> *************
✅ MQTT主题: nitamade/jiushe/ge/shabi -> `$dp

## 您的OneNET配置信息：
- 产品ID: cF16DWy2B8
- 设备ID: stm32f103
- 访问密钥: QU9uYW44bG5pcG1NVkhLWmNPUnNGZXVVcHhDZEtQMkM=
- MQTT服务器: *************
- MQTT端口: 6002 (需要在STM32代码中修改)

## 下一步操作：

### 1. 测试修改后的程序
- 运行 ShengFan_onenet.exe
- 检查程序是否正常启动
- 查看是否有错误提示

### 2. 确保STM32代码配置正确
确保您的STM32代码中包含以下配置：
```c
// OneNET MQTT服务器配置
AT+MQTTCONN=0,"*************",6002,1

// OneNET认证配置  
AT+MQTTUSERCFG=0,1,"cF16DWy2B8","stm32f103","YOUR_ACCESS_KEY",0,0,""

// 数据发布格式 (OneNET数据点格式)
AT+MQTTPUB=0,"`$dp","{\"id\":123,\"dp\":{\"temp\":[{\"v\":25.6}]}}",1,0
```

### 3. 数据格式说明
OneNET要求的数据格式为JSON：
```json
{
  "id": 123,
  "dp": {
    "temp": [{"v": 25.6}],
    "humidity": [{"v": 60.5}]
  }
}
```

### 4. 如果遇到问题：
1. 使用备份文件 ShengFan_backup.exe 恢复原程序
2. 检查OneNET平台设备状态
3. 验证网络连接
4. 确认STM32代码中的端口为6002

### 5. 验证连接成功：
- 在OneNET平台查看设备在线状态
- 检查数据是否正常上传
- 上位机是否能接收到数据

## 文件说明：
- ShengFan.exe: 原始程序
- ShengFan_backup.exe: 备份文件
- ShengFan_onenet.exe: 修改后的程序 (使用这个)
"@
    
    $guideContent | Out-File -FilePath "C:\Users\<USER>\Desktop\package\OneNET配置指南.txt" -Encoding UTF8
    Write-Host "📝 配置指南已创建: OneNET配置指南.txt" -ForegroundColor Green
    
    Write-Host "`n🎉 修改完成！" -ForegroundColor Green
    Write-Host "`n📋 重要提醒:" -ForegroundColor Yellow
    Write-Host "1. 请测试 ShengFan_onenet.exe 是否能正常启动" -ForegroundColor White
    Write-Host "2. 确保STM32代码中端口改为6002" -ForegroundColor White
    Write-Host "3. 查看 OneNET配置指南.txt 了解详细步骤" -ForegroundColor White
    Write-Host "4. 如有问题，使用 ShengFan_backup.exe 恢复" -ForegroundColor White
    
} catch {
    Write-Host "❌ 修改失败: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
