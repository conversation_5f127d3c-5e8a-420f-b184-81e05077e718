# MQTT Configuration Modifier for OneNET
param()

Write-Host "MQTT Configuration Modifier" -ForegroundColor Green
Write-Host "=" * 50

$sourcePath = ".\ShengFan.exe"
$outputPath = ".\ShengFan_onenet.exe"

# Check source file
if (-not (Test-Path $sourcePath)) {
    Write-Host "Source file not found: $sourcePath" -ForegroundColor Red
    exit 1
}

Write-Host "Found source file: $sourcePath" -ForegroundColor Green

try {
    # Read file
    $bytes = [System.IO.File]::ReadAllBytes($sourcePath)
    Write-Host "File size: $($bytes.Length) bytes" -ForegroundColor Cyan
    
    # Define target byte sequences
    $brokerOld = [System.Text.Encoding]::UTF8.GetBytes("broker.mqttdashboard.com")
    $topicOld = [System.Text.Encoding]::UTF8.GetBytes("nitamade/jiushe/ge/shabi")
    
    # Search for target strings
    $brokerFound = $false
    $topicFound = $false
    
    for ($i = 0; $i -le ($bytes.Length - $brokerOld.Length); $i++) {
        $match = $true
        for ($j = 0; $j -lt $brokerOld.Length; $j++) {
            if ($bytes[$i + $j] -ne $brokerOld[$j]) {
                $match = $false
                break
            }
        }
        if ($match) {
            $brokerFound = $true
            break
        }
    }
    
    for ($i = 0; $i -le ($bytes.Length - $topicOld.Length); $i++) {
        $match = $true
        for ($j = 0; $j -lt $topicOld.Length; $j++) {
            if ($bytes[$i + $j] -ne $topicOld[$j]) {
                $match = $false
                break
            }
        }
        if ($match) {
            $topicFound = $true
            break
        }
    }
    
    Write-Host "Search results:" -ForegroundColor Yellow
    if ($brokerFound) {
        Write-Host "   MQTT Server: Found" -ForegroundColor Green
    } else {
        Write-Host "   MQTT Server: Not found" -ForegroundColor Red
    }
    
    if ($topicFound) {
        Write-Host "   MQTT Topic: Found" -ForegroundColor Green
    } else {
        Write-Host "   MQTT Topic: Not found" -ForegroundColor Red
    }
    
    if (-not ($brokerFound -or $topicFound)) {
        Write-Host "No target configurations found" -ForegroundColor Red
        exit 1
    }
    
    # Create modified byte array
    $modifiedBytes = $bytes.Clone()
    $replacementCount = 0
    
    if ($brokerFound) {
        # Replace server address
        $brokerNew = [System.Text.Encoding]::UTF8.GetBytes("*************")
        $padding = New-Object byte[] ($brokerOld.Length - $brokerNew.Length)
        $brokerNewPadded = $brokerNew + $padding
        
        # Find and replace
        for ($i = 0; $i -le ($modifiedBytes.Length - $brokerOld.Length); $i++) {
            $match = $true
            for ($j = 0; $j -lt $brokerOld.Length; $j++) {
                if ($modifiedBytes[$i + $j] -ne $brokerOld[$j]) {
                    $match = $false
                    break
                }
            }
            if ($match) {
                for ($j = 0; $j -lt $brokerNewPadded.Length; $j++) {
                    $modifiedBytes[$i + $j] = $brokerNewPadded[$j]
                }
                $replacementCount++
                Write-Host "Replaced server: broker.mqttdashboard.com -> *************" -ForegroundColor Green
                break
            }
        }
    }
    
    if ($topicFound) {
        # Replace topic
        $topicNew = [System.Text.Encoding]::UTF8.GetBytes("`$dp")
        $padding = New-Object byte[] ($topicOld.Length - $topicNew.Length)
        $topicNewPadded = $topicNew + $padding
        
        # Find and replace
        for ($i = 0; $i -le ($modifiedBytes.Length - $topicOld.Length); $i++) {
            $match = $true
            for ($j = 0; $j -lt $topicOld.Length; $j++) {
                if ($modifiedBytes[$i + $j] -ne $topicOld[$j]) {
                    $match = $false
                    break
                }
            }
            if ($match) {
                for ($j = 0; $j -lt $topicNewPadded.Length; $j++) {
                    $modifiedBytes[$i + $j] = $topicNewPadded[$j]
                }
                $replacementCount++
                Write-Host "Replaced topic: nitamade/jiushe/ge/shabi -> `$dp" -ForegroundColor Green
                break
            }
        }
    }
    
    # Save modified file
    [System.IO.File]::WriteAllBytes($outputPath, $modifiedBytes)
    Write-Host "Modification completed: $outputPath" -ForegroundColor Green
    Write-Host "Total replacements: $replacementCount" -ForegroundColor Cyan
    
    Write-Host "`nModification completed successfully!" -ForegroundColor Green
    Write-Host "`nImportant reminders:" -ForegroundColor Yellow
    Write-Host "1. Test ShengFan_onenet.exe to ensure it starts normally" -ForegroundColor White
    Write-Host "2. Ensure STM32 code port is changed to 6002" -ForegroundColor White
    Write-Host "3. Use ShengFan_backup.exe to restore if needed" -ForegroundColor White
    
} catch {
    Write-Host "Modification failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
