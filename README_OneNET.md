# OneNET智能电表上位机使用说明

## 概述
本项目将原有的智能电表系统从broker.mqttdashboard.com迁移到OneNET物联网平台，实现更稳定和专业的数据传输。

## 系统架构
- **STM32F103**: 电表数据采集和ESP8266控制
- **ESP8266**: WiFi通信模块，连接OneNET MQTT服务器
- **Qt上位机**: 接收并显示电表数据

## OneNET平台配置
- **产品ID**: cF16DWy2B8
- **设备名称**: stm32f103
- **MQTT服务器**: *************:6002
- **认证方式**: 用户名/密码认证

## 文件说明

### 配置文件
- `mqtt_config.ini`: MQTT连接配置文件
- `start_onenet.bat`: 启动脚本（设置环境变量）

### Qt源代码
- `OneNET_Client.pro`: Qt项目文件
- `main.cpp`: 主程序入口
- `mainwindow.h/cpp`: 主窗口界面
- `mqttclient.h/cpp`: MQTT客户端封装

### 构建工具
- `build.bat`: 编译脚本
- `stm32_onenet_fix.txt`: STM32代码修正建议

## 使用方法

### 方案A: 使用新开发的Qt上位机（推荐）

1. **编译程序**
   ```bash
   build.bat
   ```

2. **运行程序**
   ```bash
   # 方式1: 直接运行
   OneNET_Client.exe
   
   # 方式2: 使用配置启动
   start_onenet.bat
   ```

### 方案B: 修改原有ShengFan.exe配置

1. **使用环境变量方式**
   ```bash
   start_onenet.bat
   ```

2. **使用配置文件方式**
   - 确保`mqtt_config.ini`存在
   - 运行`ShengFan.exe`（如果支持QSettings配置读取）

## 配置参数说明

### MQTT连接参数
```ini
[MQTT]
host=*************
port=6002
clientId=stm32f103
username=cF16DWy2B8
password=version=2018-10-31&res=products%2FcF16DWy2B8%2Fdevices%2Fstm32f103&et=2066666954&method=md5&sign=%2FpsAUgjyYBfZO53tn%2FYK7Q%3D%3D
```

### OneNET主题格式
- **订阅主题**: `$sys/cF16DWy2B8/stm32f103/thing/property/set`
- **发布主题**: `$sys/cF16DWy2B8/stm32f103/thing/property/post`

## 数据格式

### STM32发送格式（JSON）
```json
{
  "id": "消息ID",
  "version": "1.0",
  "params": {
    "voltage": 220.5,
    "current": 1.2,
    "frequency": 50.0,
    "active_power": 264.6,
    "total_energy": 123.45
  }
}
```

### 上位机显示
- 电压 (V)
- 电流 (A)  
- 频率 (Hz)
- 有功功率 (W)
- 总电能 (kWh)

## 故障排除

### 常见问题

1. **MQTT连接失败**
   - 检查网络连接
   - 验证OneNET平台配置
   - 确认token未过期

2. **编译失败**
   - 检查Qt安装路径
   - 确认Qt5Mqtt模块可用
   - 修改build.bat中的QT_DIR路径

3. **数据接收异常**
   - 检查STM32代码中的主题格式
   - 验证JSON数据格式
   - 查看上位机日志信息

### 调试方法
1. 查看上位机运行日志
2. 使用MQTT调试工具验证连接
3. 检查OneNET平台设备状态

## 技术特点

### 优势
- **稳定性**: 使用OneNET专业物联网平台
- **安全性**: 基于token的认证机制
- **可扩展性**: 支持物模型和规则引擎
- **易维护**: 配置文件和环境变量支持

### 兼容性
- 保持原有数据格式兼容
- 支持多种配置方式
- 向后兼容原有系统

## 更新日志
- v1.0: 完成OneNET平台迁移
- 支持环境变量和配置文件两种配置方式
- 新增Qt上位机程序
- 优化MQTT连接稳定性