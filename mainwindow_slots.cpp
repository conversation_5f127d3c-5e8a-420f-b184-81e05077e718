// MainWindow槽函数实现
#include "mainwindow.h"

void MainWindow::connectMqtt()
{
    logMessage("正在连接OneNET MQTT服务器...");
    m_mqttClient->connectToServer();
}

void MainWindow::disconnectMqtt()
{
    logMessage("断开MQTT连接");
    m_mqttClient->disconnectFromServer();
}

void MainWindow::onMqttConnected()
{
    logMessage("MQTT连接成功！");
    updateConnectionUI();
    statusBar()->showMessage("MQTT已连接");
}

void MainWindow::onMqttDisconnected()
{
    logMessage("MQTT连接已断开");
    updateConnectionUI();
    statusBar()->showMessage("MQTT已断开");
}

void MainWindow::onMessageReceived(const QString &topic, const QString &message)
{
    m_messageCount++;
    logMessage(QString("收到消息 [%1]: %2").arg(topic, message));
    
    // 解析电表数据
    parseElectricMeterData(message);
    
    statusBar()->showMessage(QString("已接收 %1 条消息").arg(m_messageCount));
}

void MainWindow::onConnectionStatusChanged(const QString &status)
{
    m_statusLabel->setText(status);
    
    if (status.contains("错误")) {
        m_statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
    } else if (status == "已连接") {
        m_statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
    } else {
        m_statusLabel->setStyleSheet("QLabel { color: orange; font-weight: bold; }");
    }
}

void MainWindow::sendTestMessage()
{
    if (m_mqttClient->isConnected()) {
        QString testData = QString("{\"id\":\"test_%1\",\"voltage\":220.5,\"current\":1.2,\"power\":264.6,\"energy\":123.45}")
                          .arg(QDateTime::currentMSecsSinceEpoch());
        
        m_mqttClient->publish("$sys/cF16DWy2B8/stm32f103/thing/property/post", testData);
        logMessage("发送测试数据: " + testData);
    } else {
        logMessage("错误: MQTT未连接，无法发送测试消息");
    }
}

void MainWindow::clearLog()
{
    m_logEdit->clear();
    logMessage("日志已清空");
}

void MainWindow::updateConnectionUI()
{
    bool connected = m_mqttClient->isConnected();
    m_connectBtn->setEnabled(!connected);
    m_disconnectBtn->setEnabled(connected);
    m_testBtn->setEnabled(connected);
}

void MainWindow::logMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);
    m_logEdit->append(logEntry);
    
    // 自动滚动到底部
    QTextCursor cursor = m_logEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logEdit->setTextCursor(cursor);
}

void MainWindow::parseElectricMeterData(const QString &data)
{
    // 尝试解析JSON格式的数据
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data.toUtf8(), &error);
    
    if (error.error == QJsonParseError::NoError && doc.isObject()) {
        QJsonObject obj = doc.object();
        
        // 解析电表参数
        if (obj.contains("voltage")) {
            double voltage = obj["voltage"].toDouble();
            m_voltageLabel->setText(QString("电压: %1 V").arg(voltage, 0, 'f', 1));
        }
        
        if (obj.contains("current")) {
            double current = obj["current"].toDouble();
            m_currentLabel->setText(QString("电流: %1 A").arg(current, 0, 'f', 2));
        }
        
        if (obj.contains("power")) {
            double power = obj["power"].toDouble();
            m_powerLabel->setText(QString("功率: %1 W").arg(power, 0, 'f', 1));
        }
        
        if (obj.contains("energy")) {
            double energy = obj["energy"].toDouble();
            m_energyLabel->setText(QString("电能: %1 kWh").arg(energy, 0, 'f', 2));
        }
        
        m_lastUpdateLabel->setText("最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    } else {
        // 如果不是JSON格式，尝试解析简单的文本格式
        // 假设数据格式为: "220.5,1.2,264.6,123.45" (电压,电流,功率,电能)
        QStringList values = data.split(',');
        if (values.size() >= 4) {
            bool ok;
            double voltage = values[0].toDouble(&ok);
            if (ok) m_voltageLabel->setText(QString("电压: %1 V").arg(voltage, 0, 'f', 1));
            
            double current = values[1].toDouble(&ok);
            if (ok) m_currentLabel->setText(QString("电流: %1 A").arg(current, 0, 'f', 2));
            
            double power = values[2].toDouble(&ok);
            if (ok) m_powerLabel->setText(QString("功率: %1 W").arg(power, 0, 'f', 1));
            
            double energy = values[3].toDouble(&ok);
            if (ok) m_energyLabel->setText(QString("电能: %1 kWh").arg(energy, 0, 'f', 2));
            
            m_lastUpdateLabel->setText("最后更新: " + QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
        }
    }
}